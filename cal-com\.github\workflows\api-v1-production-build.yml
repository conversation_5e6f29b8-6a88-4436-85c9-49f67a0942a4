name: Production Builds

on:
  workflow_call:

permissions:
  contents: read

env:
  ALLOWED_HOSTNAMES: ${{ vars.CI_ALLOWED_HOSTNAMES }}
  CALENDSO_ENCRYPTION_KEY: ${{ secrets.CI_CALENDSO_ENCRYPTION_KEY }}
  DATABASE_URL: ${{ secrets.CI_DATABASE_URL }}
  DATABASE_DIRECT_URL: ${{ secrets.CI_DATABASE_URL }}
  E2E_TEST_APPLE_CALENDAR_EMAIL: ${{ secrets.E2E_TEST_APPLE_CALENDAR_EMAIL }}
  E2E_TEST_APPLE_CALENDAR_PASSWORD: ${{ secrets.E2E_TEST_APPLE_CALENDAR_PASSWORD }}
  E2E_TEST_CALCOM_QA_EMAIL: ${{ secrets.E2E_TEST_CALCOM_QA_EMAIL }}
  E2E_TEST_CALCOM_QA_PASSWORD: ${{ secrets.E2E_TEST_CALCOM_QA_PASSWORD }}
  E2E_TEST_CALCOM_QA_GCAL_CREDENTIALS: ${{ secrets.E2E_TEST_CALCOM_QA_GCAL_CREDENTIALS }}
  E2E_TEST_CALCOM_GCAL_KEYS: ${{ secrets.E2E_TEST_CALCOM_GCAL_KEYS }}
  GOOGLE_API_CREDENTIALS: ${{ secrets.CI_GOOGLE_API_CREDENTIALS }}
  GOOGLE_LOGIN_ENABLED: ${{ vars.CI_GOOGLE_LOGIN_ENABLED }}
  NEXTAUTH_SECRET: ${{ secrets.CI_NEXTAUTH_SECRET }}
  NEXTAUTH_URL: ${{ secrets.CI_NEXTAUTH_URL }}
  NEXT_PUBLIC_API_V2_URL: ${{ secrets.CI_NEXT_PUBLIC_API_V2_URL }}
  NEXT_PUBLIC_API_V2_ROOT_URL: ${{ secrets.CI_NEXT_PUBLIC_API_V2_ROOT_URL }}
  NEXT_PUBLIC_IS_E2E: ${{ vars.CI_NEXT_PUBLIC_IS_E2E }}
  NEXT_PUBLIC_ORG_SELF_SERVE_ENABLED: ${{ vars.CI_NEXT_PUBLIC_ORG_SELF_SERVE_ENABLED }}
  NEXT_PUBLIC_STRIPE_PUBLIC_KEY: ${{ secrets.CI_NEXT_PUBLIC_STRIPE_PUBLIC_KEY }}
  NEXT_PUBLIC_WEBAPP_URL: ${{ vars.CI_NEXT_PUBLIC_WEBAPP_URL }}
  NEXT_PUBLIC_WEBSITE_URL: ${{ vars.CI_NEXT_PUBLIC_WEBSITE_URL }}
  PAYMENT_FEE_FIXED: ${{ vars.CI_PAYMENT_FEE_FIXED }}
  PAYMENT_FEE_PERCENTAGE: ${{ vars.CI_PAYMENT_FEE_PERCENTAGE }}
  SAML_ADMINS: ${{ secrets.CI_SAML_ADMINS }}
  SAML_DATABASE_URL: ${{ secrets.CI_SAML_DATABASE_URL }}
  STRIPE_PRIVATE_KEY: ${{ secrets.CI_STRIPE_PRIVATE_KEY }}
  STRIPE_CLIENT_ID: ${{ secrets.CI_STRIPE_CLIENT_ID }}
  STRIPE_WEBHOOK_SECRET: ${{ secrets.CI_STRIPE_WEBHOOK_SECRET }}
  SENDGRID_API_KEY: ${{ secrets.CI_SENDGRID_API_KEY }}
  SENDGRID_EMAIL: ${{ secrets.CI_SENDGRID_EMAIL }}
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

jobs:
  build:
    name: Build API v1
    runs-on: buildjet-4vcpu-ubuntu-2204
    timeout-minutes: 30
    services:
      postgres:
        image: postgres:13
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: calendso
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - uses: ./.github/actions/cache-db
      - name: Cache API v1 production build
        uses: buildjet/cache@v4
        id: cache-api-v1-build
        env:
          cache-name: api-v1-build
          key-1: ${{ hashFiles('yarn.lock') }}
          key-2: ${{ hashFiles('apps/api/v1/**.[jt]s', 'apps/api/v1/**.[jt]sx', '!**/node_modules') }}
          key-3: ${{ github.event.pull_request.number || github.ref }}
          # Ensures production-build.yml will always be fresh
          key-4: ${{ github.sha }}
        with:
          path: |
            ${{ github.workspace }}/apps/api/v1/.next
            **/.turbo/**
            **/dist/**
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ env.key-1 }}-${{ env.key-2 }}-${{ env.key-3 }}-${{ env.key-4 }}
      - name: Log Cache Hit
        if: steps.cache-api-v1-build.outputs.cache-hit == 'true'
        run: echo "Cache hit for API v1 build. Skipping build."
      - name: Run build
        if: steps.cache-api-v1-build.outputs.cache-hit != 'true'
        run: |
          export NODE_OPTIONS="--max_old_space_size=8192"
          yarn turbo run build --filter=@calcom/api...
        shell: bash
