"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./app/contact/page.tsx":
/*!******************************!*\
  !*** ./app/contact/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _calcom_embed_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @calcom/embed-react */ \"(app-pages-browser)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Select components are not used in this file as we're using native select\n\n\n\n\n\n\n// Wrapper component that uses searchParams\nfunction ContactForm() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        services: [],\n        message: ''\n    });\n    // State to track which category is expanded\n    const [expandedCategory, setExpandedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle URL query parameters on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            const serviceParam = searchParams ? searchParams.get('service') : null;\n            if (serviceParam) {\n                // If the service is a chatbot option, expand the chatbot category\n                if (serviceParam === 'chatbot' || serviceParam.startsWith('chatbot-')) {\n                    setExpandedCategory('chatbot');\n                    // If it's a specific chatbot service, select it\n                    if (serviceParam === 'chatbot-mvp' || serviceParam === 'chatbot-custom') {\n                        setFormData({\n                            \"ContactForm.useEffect\": (prev)=>({\n                                    ...prev,\n                                    services: [\n                                        serviceParam\n                                    ]\n                                })\n                        }[\"ContactForm.useEffect\"]);\n                    }\n                } else if (serviceParam === 'webdev' || serviceParam.startsWith('webdev-')) {\n                    setExpandedCategory('webdev');\n                    // If it's a specific web development service, select it\n                    if ([\n                        'webdev-essential',\n                        'webdev-business',\n                        'webdev-advanced'\n                    ].includes(serviceParam)) {\n                        setFormData({\n                            \"ContactForm.useEffect\": (prev)=>({\n                                    ...prev,\n                                    services: [\n                                        serviceParam\n                                    ]\n                                })\n                        }[\"ContactForm.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"ContactForm.useEffect\"], [\n        searchParams\n    ]);\n    // Function to handle category selection\n    const handleCategorySelect = (category)=>{\n        if (expandedCategory === category) {\n            // If clicking the same category, collapse it\n            setExpandedCategory(null);\n        } else {\n            // Expand the clicked category\n            setExpandedCategory(category);\n        }\n        // Clear error for service field when category changes\n        if (formErrors.service) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors.service;\n                return newErrors;\n            });\n        }\n    };\n    // Function to handle service option selection (supports multiple, but only one per category)\n    const handleServiceSelect = (service)=>{\n        setFormData((prev)=>{\n            const isSelected = prev.services.includes(service);\n            let newServices = [\n                ...prev.services\n            ];\n            if (isSelected) {\n                // Remove if already selected\n                newServices = newServices.filter((s)=>s !== service);\n            } else {\n                // Add the new service, but first remove any other service from the same category\n                if (service.startsWith('chatbot-')) {\n                    // Remove any existing chatbot services\n                    newServices = newServices.filter((s)=>!s.startsWith('chatbot-'));\n                } else if (service.startsWith('webdev-')) {\n                    // Remove any existing webdev services\n                    newServices = newServices.filter((s)=>!s.startsWith('webdev-'));\n                }\n                // Add the new service\n                newServices.push(service);\n            }\n            return {\n                ...prev,\n                services: newServices\n            };\n        });\n        // Clear error for service field when value changes\n        if (formErrors.service) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors.service;\n                return newErrors;\n            });\n        }\n    };\n    // Function to remove a selected service\n    const removeService = (serviceToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                services: prev.services.filter((service)=>service !== serviceToRemove)\n            }));\n    };\n    // Add CSS animation for the interactive service selection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            // Add the animation CSS if it doesn't exist\n            if (!document.getElementById('service-animation-styles')) {\n                const styleEl = document.createElement('style');\n                styleEl.id = 'service-animation-styles';\n                styleEl.textContent = \"\\n        @keyframes fadeIn {\\n          from { opacity: 0; transform: translateY(-10px); }\\n          to { opacity: 1; transform: translateY(0); }\\n        }\\n        .animate-fadeIn {\\n          animation: fadeIn 0.3s ease-out forwards;\\n        }\\n      \";\n                document.head.appendChild(styleEl);\n            }\n            return ({\n                \"ContactForm.useEffect\": ()=>{\n                    // Clean up the style element when component unmounts\n                    const styleEl = document.getElementById('service-animation-styles');\n                    if (styleEl) styleEl.remove();\n                }\n            })[\"ContactForm.useEffect\"];\n        }\n    }[\"ContactForm.useEffect\"], []);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // For general API errors\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormErrors({}); // Clear previous errors\n        setSubmitError('');\n        setSubmitSuccess(false);\n        // --- Validation ---\n        const errors = {};\n        if (!formData.name.trim()) errors.name = 'Name is required.';\n        if (!formData.email.trim()) {\n            errors.email = 'Email is required.';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            errors.email = 'Email is invalid.';\n        }\n        if (formData.services.length === 0) errors.service = 'Please select at least one service.';\n        if (!formData.message.trim()) errors.message = 'Message is required.';\n        if (Object.keys(errors).length > 0) {\n            setFormErrors(errors);\n            return; // Stop submission if validation fails\n        }\n        // --- End Validation ---\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    service: formData.services.join(', ') // Convert array to string for backward compatibility\n                })\n            });\n            if (!response.ok) {\n                let errorMessage = 'Failed to submit form';\n                try {\n                    const errorData = await response.json();\n                    errorMessage = errorData.error || errorMessage;\n                } catch (e) {\n                // If response is not JSON, use default message\n                }\n                throw new Error(errorMessage);\n            }\n            await response.json(); // Consume the response\n            setSubmitSuccess(true);\n            setFormData({\n                name: '',\n                email: '',\n                services: [],\n                message: ''\n            });\n        } catch (error) {\n            console.error('Contact form submission error:', error);\n            setSubmitError(error instanceof Error ? error.message : 'Something went wrong. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear the error for the field being changed\n        if (formErrors[name]) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors[name];\n                return newErrors;\n            });\n        }\n    };\n    // We're using the native select element with the handleChange function\n    // Use useRef to persist initialization state across re-renders\n    const isInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Cal.com embed initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            ({\n                \"ContactForm.useEffect\": async function() {\n                    const cal = await (0,_calcom_embed_react__WEBPACK_IMPORTED_MODULE_7__.getCalApi)();\n                    cal(\"ui\", {\n                        \"theme\": \"dark\",\n                        \"styles\": {\n                            \"branding\": {\n                                \"brandColor\": \"#7c3aed\"\n                            }\n                        },\n                        \"hideEventTypeDetails\": false\n                    });\n                }\n            })[\"ContactForm.useEffect\"]();\n        }\n    }[\"ContactForm.useEffect\"], []);\n    // ORIGINAL CALENDLY CODE - COMMENTED OUT\n    /*\r\n  useEffect(() => {\r\n  //   const initCalendly = () => {\r\n  //     if (isInitializedRef.current) {\r\n  //       console.log('⚠️ Calendly already initialized, skipping...');\r\n  //       return;\r\n  //     }\r\n\r\n  //     const widgetElement = document.querySelector('.calendly-inline-widget');\r\n  //     if (!widgetElement) {\r\n  //       console.warn('⚠️ Calendly widget element not found');\r\n  //       return;\r\n  //     }\r\n\r\n  //     // Clear any existing content to prevent duplicates\r\n  //     widgetElement.innerHTML = `\r\n  //       <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //         <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mb-4\"></div>\r\n  //         <p class=\"text-purple-100/70 mb-4\">Loading calendar...</p>\r\n  //       </div>\r\n  //     `;\r\n\r\n  //     if (!window.Calendly) {\r\n  //       console.warn('⚠️ Calendly script not loaded yet, retrying...');\r\n  //       setTimeout(initCalendly, 500);\r\n  //       return;\r\n  //     }\r\n\r\n  //     try {\r\n  //       console.log('🔄 Attempting to initialize Calendly inline widget...');\r\n  //       console.log('📍 Widget element:', widgetElement);\r\n  //       console.log('🌐 Calendly object:', window.Calendly);\r\n\r\n  //       // Clear the loading message\r\n  //       widgetElement.innerHTML = '';\r\n\r\n  //       window.Calendly.initInlineWidget({\r\n  //         url: 'https://calendly.com/pilybas-edgaras/30min',\r\n  //         parentElement: widgetElement as HTMLElement,\r\n  //         prefill: {},\r\n  //         utm: {}\r\n  //       });\r\n  //       isInitializedRef.current = true;\r\n  //       console.log('✅ Calendly inline widget initialized successfully');\r\n  //     } catch (error) {\r\n  //       console.error('❌ Calendly initialization failed:', error);\r\n  //       // Show error message to user\r\n  //       widgetElement.innerHTML = `\r\n  //         <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //           <p class=\"text-red-400 mb-4\">Failed to load calendar</p>\r\n  //           <p class=\"text-purple-100/70 text-sm\">Please try refreshing the page</p>\r\n  //         </div>\r\n  //       `;\r\n  //     }\r\n  //   };\r\n\r\n  //   // Check if Calendly script is already loaded\r\n  //   if (window.Calendly) {\r\n  //     console.log('✅ Calendly already loaded, initializing...');\r\n  //     initCalendly();\r\n  //     return;\r\n  //   }\r\n\r\n  //   // Check if script is already in DOM\r\n  //   const existingScript = document.querySelector('script[src*=\"calendly.com\"]');\r\n  //   if (existingScript) {\r\n  //     console.log('⏳ Calendly script already in DOM, waiting for load...');\r\n  //     existingScript.addEventListener('load', initCalendly);\r\n  //     return;\r\n  //   }\r\n\r\n  //   // Load Calendly script only once\r\n  //   console.log('📥 Loading Calendly script...');\r\n  //   const script = document.createElement('script');\r\n  //   script.src = 'https://assets.calendly.com/assets/external/widget.js';\r\n  //   script.async = true;\r\n  //   script.id = 'calendly-script'; // Add ID to prevent duplicates\r\n\r\n  //   script.onload = () => {\r\n  //     console.log('✅ Calendly script loaded successfully');\r\n  //     setTimeout(initCalendly, 100);\r\n  //   };\r\n\r\n  //   script.onerror = (error) => {\r\n  //     console.error('❌ Failed to load Calendly script:', error);\r\n  //     // Show fallback message\r\n  //     const widgetElement = document.querySelector('.calendly-inline-widget');\r\n  //     if (widgetElement) {\r\n  //       widgetElement.innerHTML = `\r\n  //         <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //           <p class=\"text-red-400 mb-4\">Unable to load calendar</p>\r\n  //           <p class=\"text-purple-100/70 text-sm mb-4\">Please book directly:</p>\r\n  //           <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\r\n  //              class=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\">\r\n  //             Open Calendar in New Tab\r\n  //           </a>\r\n  //         </div>\r\n  //       `;\r\n  //     }\r\n  //   };\r\n\r\n  //   document.head.appendChild(script);\r\n\r\n  //   return () => {\r\n  //     // Cleanup - but don't reset isInitializedRef as it should persist\r\n  //     // The widget will be cleaned up when the component unmounts\r\n  //   };\r\n  // }, []); // Empty dependency array to run only once\r\n  */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl font-bold text-white mb-4\",\n                                            children: \"Do not hesitate to get in touch\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-purple-100/70\",\n                                            children: \"We will do our best to answer all your questions and provide you with our services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"+31 610 271 038\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"Laplace-gebouw, Laplace 32, 5612 AJ Eindhoven\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"contact-form\",\n                                className: \"bg-white rounded-2xl p-8 shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                        children: \"Contact us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading contact form...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"contact-form\",\n                                className: \"bg-white rounded-2xl p-8 shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                        children: \"Contact us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        className: \"space-y-6\",\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"name\",\n                                                        name: \"name\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors\",\n                                                        placeholder: \"Your name\",\n                                                        value: formData.name,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 39\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors\",\n                                                        type: \"email\",\n                                                        placeholder: \"<EMAIL>\",\n                                                        value: formData.email,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-3 relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-purple-500/5 rounded-full blur-xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-6 -left-6 w-12 h-12 bg-blue-500/5 rounded-full blur-xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'chatbot' ? 'ring-4 ring-purple-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>handleCategorySelect('chatbot'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-purple-600 to-pink-500 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 492,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 493,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Chatbot Integration\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 495,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M8 10L12 14L16 10\",\n                                                                                                        stroke: \"currentColor\",\n                                                                                                        strokeWidth: \"2\",\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                        lineNumber: 498,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 497,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 496,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 494,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"AI-powered chat solutions\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 502,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-purple-600/10 to-pink-500/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'webdev' ? 'ring-4 ring-blue-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>handleCategorySelect('webdev'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-blue-600 to-cyan-500 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 516,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 517,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Web Development\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 519,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M8 10L12 14L16 10\",\n                                                                                                        stroke: \"currentColor\",\n                                                                                                        strokeWidth: \"2\",\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                        lineNumber: 522,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 521,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 520,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"Custom website solutions\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 526,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 to-cyan-500/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'other' || formData.services.includes('other') ? 'ring-4 ring-gray-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>{\n                                                                            handleCategorySelect('other');\n                                                                            handleServiceSelect('other');\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-gray-700 to-gray-600 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 543,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Other Services\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 546,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M12 5V19\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                            lineNumber: 549,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M5 12H19\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                            lineNumber: 550,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 548,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 547,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"Custom requirements\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 554,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-gray-700/10 to-gray-600/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 558,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            expandedCategory === 'chatbot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pl-4 border-l-4 border-purple-500 space-y-3 animate-fadeIn mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('chatbot-mvp') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('chatbot-mvp'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('chatbot-mvp') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"MVP Virtual Assistant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 572,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Essential chatbot features for your business\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 573,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('chatbot-custom') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('chatbot-custom'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('chatbot-custom') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Customizable AI Assistant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 585,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Advanced features with deep integrations\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 586,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            expandedCategory === 'webdev' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pl-4 border-l-4 border-blue-500 space-y-3 animate-fadeIn mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-essential') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-essential'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-essential') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 601,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Website Essentials\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 603,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Basic website with core functionality\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-business') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-business'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-business') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 614,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Smart Business Websites\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 616,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Enhanced features for growing businesses\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-advanced') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-advanced'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-advanced') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Advanced Web Platforms\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 629,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Complex solutions with custom functionality\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 630,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.service && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.service\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.services.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Selected Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.services.map((service)=>{\n                                                            const serviceLabels = {\n                                                                'chatbot-mvp': 'MVP Virtual Assistant',\n                                                                'chatbot-custom': 'Customizable AI Assistant',\n                                                                'webdev-essential': 'Website Essentials',\n                                                                'webdev-business': 'Smart Business Websites',\n                                                                'webdev-advanced': 'Advanced Web Platforms',\n                                                                'other': 'Other Services'\n                                                            };\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: serviceLabels[service] || service\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeService(service),\n                                                                        className: \"ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors duration-200\",\n                                                                        \"aria-label\": \"Remove \".concat(serviceLabels[service] || service),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"14\",\n                                                                            height: \"14\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M18 6L6 18\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    strokeWidth: \"2\",\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M6 6L18 18\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    strokeWidth: \"2\",\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, service, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Message\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                        id: \"message\",\n                                                        name: \"message\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 min-h-[150px] transition-colors\",\n                                                        placeholder: \"Your message\",\n                                                        value: formData.message,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 15\n                                            }, this),\n                                            submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: submitError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center justify-center space-y-2 animate-fade-in\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-20 h-20 text-green-500 animate-outgoing-pop\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-500 text-sm transition-opacity duration-500 ease-out\",\n                                                        children: \"Thank you! Your message has been sent.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105\",\n                                                disabled: isSubmitting,\n                                                children: isSubmitting ? 'Sending...' : 'Contact'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative my-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t-2 border-purple-500/50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 731,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 px-6 py-2 text-3xl font-bold text-white rounded-full shadow-lg\",\n                                children: \"OR!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 730,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Schedule a Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-purple-100/70\",\n                                    children: \"Loading calendar...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 11\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"Schedule a Meeting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-purple-100/70\",\n                                        children: \"Book a free consultation call with our team\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-900/30 backdrop-blur-sm rounded-2xl p-0 border border-purple-700/20 w-full max-w-5xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-2xl overflow-hidden\",\n                                        style: {\n                                            minWidth: \"320px\",\n                                            height: \"700px\",\n                                            width: \"100%\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_calcom_embed_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            calOrigin: \"https://cal.com\",\n                                            calLink: \"bailey/15min\",\n                                            style: {\n                                                width: \"100%\",\n                                                height: \"700px\",\n                                                overflow: \"scroll\"\n                                            },\n                                            config: {\n                                                \"theme\": \"dark\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 752,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 742,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 382,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"Pk26HUivDWhh4rA0nWLhSq6M94s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ContactForm;\n// Main page component that wraps ContactForm in Suspense\nfunction ContactPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 782,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 781,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 784,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 781,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ContactPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ContactForm\");\n$RefreshReg$(_c1, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contact/page.tsx\n"));

/***/ })

});