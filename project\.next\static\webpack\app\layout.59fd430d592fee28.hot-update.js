"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5ddb5e255d3e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaW5nYXBcXE9uZURyaXZlXFxTdGFsaW5pcyBrb21waXV0ZXJpc1xcQ29kZV9Qcm9qZWN0c1xcVXBaZXJhXFxVcFplcmEgKG5ld2VzdClcXHByb2plY3RcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZGRiNWUyNTVkM2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/chatbot/ChatMessage.tsx":
/*!********************************************!*\
  !*** ./components/chatbot/ChatMessage.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./components/chatbot/QuickActions.tsx\");\n/* harmony import */ var _ServiceOptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ServiceOptions */ \"(app-pages-browser)/./components/chatbot/ServiceOptions.tsx\");\n/* harmony import */ var _MainMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MainMenu */ \"(app-pages-browser)/./components/chatbot/MainMenu.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// Enhanced function to format bot messages with proper spacing and structure\nfunction formatBotMessage(text) {\n    if (!text) return text;\n    // Enhanced text formatting with automatic spacing detection\n    let formattedText = text;\n    // Add line breaks after sentences that end with periods, exclamation marks, or question marks\n    // but only if they're followed by a capital letter or \"For example\" (indicating a new sentence/section)\n    formattedText = formattedText.replace(/([.!?])\\s+([A-Z]|For example)/g, '$1\\n\\n$2');\n    // Add line breaks before \"For example,\" patterns\n    formattedText = formattedText.replace(/\\s+(For example,)/gi, '\\n\\n$1');\n    // Add line breaks after price ranges and before new information\n    formattedText = formattedText.replace(/(€\\d+[,\\d]*(?:\\s*to\\s*€\\d+[,\\d]*)?[.,]?)\\s+([A-Z])/g, '$1\\n\\n$2');\n    // Add line breaks before \"After\" patterns (like \"After a free consultation\")\n    formattedText = formattedText.replace(/\\s+(After\\s+)/gi, '\\n\\n$1');\n    // Add spacing around key pricing phrases that should stand out\n    formattedText = formattedText.replace(/(landing pages start from|full websites range from|chatbot setups typically cost|chatbots typically cost)/gi, '\\n\\n$1');\n    // Add line breaks before concluding statements\n    formattedText = formattedText.replace(/\\s+(After a free consultation,|You'll receive)/gi, '\\n\\n$1');\n    // Split by double newlines to create paragraphs\n    const paragraphs = formattedText.split('\\n\\n');\n    return paragraphs.map((paragraph, pIndex)=>{\n        // Skip empty paragraphs\n        if (!paragraph.trim()) return null;\n        // Split by single newlines within paragraphs\n        const lines = paragraph.split('\\n');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: pIndex > 0 ? 'mt-4' : '',\n            children: lines.map((line, lIndex)=>{\n                // Skip empty lines\n                if (!line.trim()) return null;\n                // Process bold formatting (**text**)\n                const parts = line.split(/(\\*\\*.*?\\*\\*)/g);\n                const formattedLine = parts.map((part, partIndex)=>{\n                    if (part.startsWith('**') && part.endsWith('**')) {\n                        const boldText = part.slice(2, -2);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-semibold\",\n                            children: boldText\n                        }, partIndex, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 22\n                        }, this);\n                    }\n                    return part;\n                });\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: lIndex > 0 ? 'mt-2' : '',\n                    children: formattedLine\n                }, lIndex, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 13\n                }, this);\n            })\n        }, pIndex, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }).filter(Boolean); // Remove null entries\n}\nfunction ChatMessage(param) {\n    let { message, screenSize, onQuickAction } = param;\n    const isUser = message.isUser;\n    // Get screen size properties\n    const isSmall = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.isSmall) || false;\n    const width = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.width) || 1200;\n    // Adjust spacing and sizing based on screen size\n    let messageSpacing = 'mb-6';\n    let avatarSize = 'w-8 h-8';\n    let messagePadding = 'p-3';\n    let maxWidth = 'max-w-[85%]';\n    let dotSize = 'w-2 h-2';\n    if (isSmall) {\n        if (width <= 320) {\n            messageSpacing = 'mb-3';\n            avatarSize = 'w-5 h-5';\n            messagePadding = 'p-1.5';\n            maxWidth = 'max-w-[92%]';\n            dotSize = 'w-1 h-1';\n        } else if (width <= 375) {\n            messageSpacing = 'mb-4';\n            avatarSize = 'w-6 h-6';\n            messagePadding = 'p-2';\n            maxWidth = 'max-w-[90%]';\n            dotSize = 'w-1.5 h-1.5';\n        } else {\n            messageSpacing = 'mb-5';\n            avatarSize = 'w-7 h-7';\n            messagePadding = 'p-2.5';\n            maxWidth = 'max-w-[88%]';\n            dotSize = 'w-1.5 h-1.5';\n        }\n    }\n    if (message.type === 'calendly') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendlyWidget, {\n            screenSize: screenSize,\n            messageSpacing: messageSpacing\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 127,\n            columnNumber: 12\n        }, this);\n    }\n    if (message.type === 'mainMenu') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MainMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onMenuClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    if (message.type === 'quickActions') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onActionClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    if (message.type === 'serviceOptions') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ServiceOptions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onServiceClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start \".concat(isUser ? 'justify-end' : 'justify-start', \" w-full \").concat(messageSpacing),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-2 \".concat(isUser ? 'flex-row-reverse space-x-reverse' : '', \" \").concat(maxWidth),\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo/chatbot_avatar.png\",\n                    alt: \"Bot Avatar\",\n                    className: \"\".concat(avatarSize, \" rounded-full flex-shrink-0\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(isUser ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-tl-xl rounded-tr-xl rounded-bl-xl shadow' : 'bg-gray-100 text-gray-800 rounded-tr-xl rounded-tl-xl rounded-br-xl shadow', \" \").concat(messagePadding, \" min-w-[40px] break-words animate-fade-in responsive-text\"),\n                    children: message.text === '...' && !isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0.2s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0.4s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, this) : isUser ? message.text : formatBotMessage(message.text)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_c = ChatMessage;\n// Calendly Widget Component\nfunction CalendlyWidget(param) {\n    let { screenSize, messageSpacing } = param;\n    _s();\n    const calendlyRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isSmall = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.isSmall) || false;\n    const width = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.width) || 1200;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CalendlyWidget.useEffect\": ()=>{\n            // TODO: Replace with Cal.com embed integration\n            const initializeCalendly = {\n                \"CalendlyWidget.useEffect.initializeCalendly\": ()=>{\n                    if (!calendlyRef.current) {\n                        return;\n                    }\n                    // Temporary placeholder for Cal.com integration\n                    calendlyRef.current.innerHTML = '\\n        <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4 bg-purple-50 rounded-lg border border-purple-200\">\\n          <div class=\"text-3xl mb-2\">\\uD83D\\uDCC5</div>\\n          <p class=\"text-purple-800 font-medium text-sm mb-1\">Calendar Upgrade</p>\\n          <p class=\"text-purple-600 text-xs\">Cal.com integration coming soon</p>\\n        </div>\\n      ';\n                }\n            }[\"CalendlyWidget.useEffect.initializeCalendly\"];\n            // ORIGINAL CALENDLY CODE - COMMENTED OUT\n            /*\n    const initializeCalendly = () => {\n      if (isInitializedRef.current || !calendlyRef.current) {\n        return;\n      }\n\n      try {\n        if (window.Calendly) {\n          // Clear any existing content\n          calendlyRef.current.innerHTML = '';\n\n          // Initialize the widget\n          window.Calendly.initInlineWidget({\n            url: 'https://calendly.com/pilybas-edgaras/30min',\n            parentElement: calendlyRef.current,\n            prefill: {},\n            utm: {}\n          });\n          isInitializedRef.current = true;\n        } else {\n          // Show loading state\n          calendlyRef.current.innerHTML = `\n            <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n              <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2\"></div>\n              <p class=\"text-gray-600 text-sm\">Loading calendar...</p>\n            </div>\n          `;\n        }\n      } catch (error) {\n        console.error('Calendly initialization error:', error);\n        // Show fallback link\n        //   calendlyRef.current.innerHTML = `\n        //     <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n        //       <p class=\"text-gray-600 text-sm mb-3\">Unable to load calendar widget</p>\n        //       <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\n        //          class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\">\n        //         Open Calendar in New Tab\n        //       </a>\n        //     </div>\n        //   `;\n        // }\n      // }\n    // };\n\n    // // Load Calendly script if not already loaded\n    // if (!window.Calendly) {\n    //   const existingScript = document.querySelector('script[src*=\"calendly.com\"]');\n    //   if (!existingScript) {\n    //     const script = document.createElement('script');\n    //     script.src = 'https://assets.calendly.com/assets/external/widget.js';\n    //     script.async = true;\n    //     script.onload = () => {\n    //       setTimeout(initializeCalendly, 100);\n    //     };\n    //     script.onerror = () => {\n    //       // Show fallback on script load error\n    //       if (calendlyRef.current) {\n    //         calendlyRef.current.innerHTML = `\n    //           <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n    //             <p class=\"text-gray-600 text-sm mb-3\">Unable to load calendar</p>\n    //             <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\n    //                class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\">\n    //               Book Meeting Directly\n    //             </a>\n    //           </div>\n    //         `;\n    //       }\n    //     };\n    //     document.head.appendChild(script);\n    //   } else {\n    //     existingScript.addEventListener('load', initializeCalendly);\n    //   }\n    // } else {\n    //   // If script is already loaded, initialize immediately\n    //   setTimeout(initializeCalendly, 100);\n    // }\n\n    // return () => {\n    //   // Cleanup: remove any existing Calendly widgets\n    //   if (calendlyRef.current) {\n    //     calendlyRef.current.innerHTML = '';\n    //   }\n    //   isInitializedRef.current = false;\n    // };\n    */ // Initialize the placeholder immediately\n            initializeCalendly();\n        }\n    }[\"CalendlyWidget.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full animate-fade-in \".concat(messageSpacing, \" flex justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-2 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: calendlyRef,\n                className: \"calendly-inline-widget\",\n                style: {\n                    minWidth: isSmall ? width <= 320 ? '220px' : '240px' : '280px',\n                    height: isSmall ? width <= 320 ? '220px' : '250px' : '300px',\n                    width: '100%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(CalendlyWidget, \"sOPyCU/urotnPJk0NImxj6Blkck=\");\n_c1 = CalendlyWidget;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatMessage\");\n$RefreshReg$(_c1, \"CalendlyWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chatbot/ChatMessage.tsx\n"));

/***/ })

});