# Cal.com App Store Environment Configuration
# For UpZera integration - minimal setup for basic functionality

# - GOOGLE CALENDAR/MEET/LOGIN **************************************************************************
# Needed to enable Google Calendar integration
# You'll need to add your Google Calendar API credentials here
GOOGLE_API_CREDENTIALS=""

# - VIDEO CONFERENCING *********************************************************************************
# Optional - can be added later if needed
DAILY_API_KEY=
DAILY_SCALE_PLAN=""
DAILY_WEBHOOK_SECRET=""

# - ZOOM INTEGRATION ***********************************************************************************
# Optional - can be added later if needed
ZOOM_CLIENT_ID=
ZOOM_CLIENT_SECRET=

# - STRIPE PAYMENTS *************************************************************************************
# Optional - can be added later if needed for paid bookings
STRIPE_PRIVATE_KEY=
STRIPE_PUBLIC_KEY=
STRIPE_WEBHOOK_SECRET=

# - OTHER INTEGRATIONS **********************************************************************************
# These are optional and can be left empty for basic functionality
HUBSPOT_CLIENT_ID=
HUBSPOT_CLIENT_SECRET=

SALESFORCE_CONSUMER_KEY=
SALESFORCE_CONSUMER_SECRET=

ZAPIER_INVITE_LINK=

# - OFFICE 365 CALENDAR ********************************************************************************
# Optional - for Office 365 calendar integration
MS_GRAPH_CLIENT_ID=
MS_GRAPH_CLIENT_SECRET=

# - APPLE CALENDAR **************************************************************************************
# Optional - for Apple calendar integration
APPLE_CALENDAR_CLIENT_ID=
APPLE_CALENDAR_CLIENT_SECRET=

# - CALDAV CALENDAR *************************************************************************************
# Optional - for CalDAV calendar integration
CALDAV_CLIENT_ID=
CALDAV_CLIENT_SECRET=
