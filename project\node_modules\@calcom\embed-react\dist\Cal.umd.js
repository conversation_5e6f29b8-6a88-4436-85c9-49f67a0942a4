(function(s,o){typeof exports=="object"&&typeof module<"u"?o(exports,require("react"),require("react/jsx-runtime")):typeof define=="function"&&define.amd?define(["exports","react","react/jsx-runtime"],o):(s=typeof globalThis<"u"?globalThis:s||self,o(s.Cal={},s.React,s.jsxRuntime))})(this,function(s,o,g){"use strict";const b="https://app.cal.com/embed/embed.js";function m(c=b){(function(r,n,a){let t=function(e,i){e.q.push(i)},u=r.document;r.Cal=r.Cal||function(){let e=r.Cal,i=arguments;if(e.loaded||(e.ns={},e.q=e.q||[],u.head.appendChild(u.createElement("script")).src=n,e.loaded=!0),i[0]===a){const d=function(){t(d,arguments)},l=i[1];d.q=d.q||[],typeof l=="string"?(e.ns[l]=e.ns[l]||d,t(e.ns[l],i),t(e,["initNamespace",l])):t(e,i);return}t(e,i)}})(window,c,"init");/*!  Copying ends here. */return window.Cal}m.toString();function h(c){const[r,n]=o.useState();return o.useEffect(()=>{n(()=>m(c))},[]),r}const q=function(r){const{calLink:n,calOrigin:a,namespace:t="",config:u,initConfig:e={},embedJsUrl:i,...d}=r;if(!n)throw new Error("calLink is required");const l=o.useRef(!1),f=h(i),p=o.useRef(null);return o.useEffect(()=>{if(!f||l.current||!p.current)return;l.current=!0;const C=p.current;t?(f("init",t,{...e,origin:a}),f.ns[t]("inline",{elementOrSelector:C,calLink:n,config:u})):(f("init",{...e,origin:a}),f("inline",{elementOrSelector:C,calLink:n,config:u}))},[f,n,u,t,a,e]),f?g.jsx("div",{ref:p,...d}):null};function w(c){const r=typeof c=="string"?{embedJsUrl:c}:c??{},{namespace:n="",embedJsUrl:a}=r;return new Promise(function t(u){const e=m(a);e("init",n);const i=n?e.ns[n]:e;if(!i){setTimeout(()=>{t(u)},50);return}u(i)})}s.default=q,s.getCalApi=w,Object.defineProperties(s,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
