"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/scrape-knowledge";
exports.ids = ["pages/api/scrape-knowledge"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-knowledge&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cscrape-knowledge.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-knowledge&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cscrape-knowledge.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\scrape-knowledge.ts */ \"(api-node)/./pages/api/scrape-knowledge.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/scrape-knowledge\",\n        pathname: \"/api/scrape-knowledge\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_scrape_knowledge_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-knowledge&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cscrape-knowledge.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/scrape-knowledge.ts":
/*!***************************************!*\
  !*** ./pages/api/scrape-knowledge.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"cheerio\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([cheerio__WEBPACK_IMPORTED_MODULE_0__]);\ncheerio__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// API endpoint for scraping website content for knowledge base\n\nasync function handler(req, res) {\n    // Add CORS headers\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');\n    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');\n    if (req.method === 'OPTIONS') {\n        res.status(200).end();\n        return;\n    }\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { urls } = req.body;\n    if (!urls || !Array.isArray(urls)) {\n        return res.status(400).json({\n            error: 'URLs array is required'\n        });\n    }\n    try {\n        const scrapedData = [];\n        for (const url of urls){\n            try {\n                console.log(`Scraping: ${url}`);\n                const response = await fetch(url, {\n                    headers: {\n                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n                }\n                const html = await response.text();\n                const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n                // Remove script and style elements\n                $('script, style, nav, footer, header, .navigation, .menu').remove();\n                // Extract title\n                const title = $('title').text().trim() || $('h1').first().text().trim() || 'Untitled';\n                // Extract main content\n                let content = '';\n                const contentSelectors = [\n                    'main',\n                    '[role=\"main\"]',\n                    '.content',\n                    '.main-content',\n                    'article',\n                    '.post-content',\n                    '.entry-content'\n                ];\n                for (const selector of contentSelectors){\n                    const element = $(selector).first();\n                    if (element.length && element.text().trim().length > 100) {\n                        content = element.text().trim();\n                        break;\n                    }\n                }\n                // If no main content found, extract from body\n                if (!content) {\n                    content = $('body').text().trim();\n                }\n                // Clean content\n                content = content.replace(/\\s+/g, ' ').replace(/\\n+/g, ' ').trim();\n                // Extract headings\n                const headings = [];\n                $('h1, h2, h3, h4, h5, h6').each((_, element)=>{\n                    const heading = $(element).text().trim();\n                    if (heading && heading.length > 3) {\n                        headings.push(heading);\n                    }\n                });\n                // Extract internal links\n                const links = [];\n                $('a[href]').each((_, element)=>{\n                    const href = $(element).attr('href');\n                    if (href && (href.startsWith('/') || href.includes(new URL(url).hostname))) {\n                        const linkText = $(element).text().trim();\n                        if (linkText && linkText.length > 3) {\n                            links.push(`${linkText}: ${href}`);\n                        }\n                    }\n                });\n                scrapedData.push({\n                    url,\n                    title,\n                    content: content.substring(0, 5000),\n                    headings: headings.slice(0, 20),\n                    links: links.slice(0, 10) // Limit links\n                });\n            } catch (error) {\n                console.error(`Error scraping ${url}:`, error);\n                scrapedData.push({\n                    url,\n                    title: 'Error',\n                    content: '',\n                    headings: [],\n                    links: [],\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                });\n            }\n        }\n        res.status(200).json({\n            success: true,\n            data: scrapedData,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Scraping error:', error);\n        res.status(500).json({\n            error: 'Failed to scrape content',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/scrape-knowledge.ts\n");

/***/ }),

/***/ "cheerio":
/*!**************************!*\
  !*** external "cheerio" ***!
  \**************************/
/***/ ((module) => {

module.exports = import("cheerio");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-knowledge&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cscrape-knowledge.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();