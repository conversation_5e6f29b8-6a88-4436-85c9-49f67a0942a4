import type { AllPossibleLayouts, EmbedThemeConfig } from "./types";
export declare function getMaxHeightForModal(): number;
export declare function getTrueLayout({ layout }: {
    layout: AllPossibleLayouts | null;
}): "month_view" | "week_view" | "column_view" | "mobile";
export declare function isThemePreferenceProvided(theme: EmbedThemeConfig | undefined | null): boolean;
export declare function getThemeClassForEmbed({ theme }: {
    theme: EmbedThemeConfig | undefined | null;
}): EmbedThemeConfig;
export declare function getColorSchemeDarkQuery(): MediaQueryList;
export declare function addDarkColorSchemeChangeListener(listener: (e: MediaQueryListEvent) => void): void;
export declare function removeDarkColorSchemeChangeListener(listener: (e: MediaQueryListEvent) => void): void;
//# sourceMappingURL=ui-utils.d.ts.map