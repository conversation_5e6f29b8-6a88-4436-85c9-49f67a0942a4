name: Cron - downgradeUsers

on:
  workflow_dispatch:
  # "Scheduled workflows run on the latest commit on the default or base branch."
  # — https://docs.github.com/en/actions/learn-github-actions/events-that-trigger-workflows#schedule
  schedule:
    # Runs "At 00:00 on day-of-month 1." (see https://crontab.guru)
    - cron: "0 0 1 * *"
jobs:
  cron-downgradeUsers:
    env:
      APP_URL: ${{ secrets.APP_URL }}
      CRON_API_KEY: ${{ secrets.CRON_API_KEY }}
    runs-on: ubuntu-latest
    steps:
      - name: cURL request
        if: ${{ env.APP_URL && env.CRON_API_KEY }}
        run: |
          curl ${{ secrets.APP_URL }}/api/cron/downgradeUsers \
            -X POST \
            -H 'content-type: application/json' \
            -H 'authorization: ${{ secrets.CRON_API_KEY }}' \
            -sSf
