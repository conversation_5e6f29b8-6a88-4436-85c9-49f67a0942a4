{"version": 3, "file": "sdk-action-manager.d.ts", "sourceRoot": "", "sources": ["../../src/sdk-action-manager.ts"], "names": [], "mappings": "AAAA,KAAK,SAAS,GAAG,MAAM,CAAC;AAWxB,MAAM,MAAM,YAAY,GAAG;IACzB,iBAAiB,EAAE;QAEjB,SAAS,EAAE,GAAG,CAAC;KAChB,CAAC;IACF,UAAU,EAAE;QACV,IAAI,EAAE,MAAM,CAAC;QACb,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE;YACJ,GAAG,EAAE,MAAM,CAAC;SACb,CAAC;KACH,CAAC;IACF,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACjC,mBAAmB,EAAE;QACnB,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;QACxB,KAAK,EAAE,MAAM,GAAG,SAAS,CAAC;QAC1B,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;QAC9B,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC;QAC5B,WAAW,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;QACvC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;QAC3B,eAAe,EAAE,OAAO,CAAC;QACzB,WAAW,EAAE,OAAO,CAAC;QACrB;;WAEG;QACH,WAAW,CAAC,EAAE;YAAE,SAAS,EAAE,MAAM,CAAC;YAAC,OAAO,EAAE,MAAM,CAAA;SAAE,EAAE,CAAC;QACvD,YAAY,CAAC,EAAE,MAAM,CAAC;KACvB,CAAC;IAEF;;OAEG;IACH,iBAAiB,EAAE;QAEjB,OAAO,EAAE,OAAO,CAAC;QACjB,SAAS,EAAE,OAAO,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,GAAG,SAAS,CAAC;QAC7B,SAAS,EAAE;YACT,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,SAAS,EAAE,OAAO,CAAC;KACpB,CAAC;IACF,6BAA6B,EAAE;QAC7B,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;QACxB,KAAK,EAAE,MAAM,GAAG,SAAS,CAAC;QAC1B,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;QAC9B,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC;QAC5B,WAAW,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;QACvC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;QAC3B,eAAe,EAAE,OAAO,CAAC;QACzB,WAAW,EAAE,OAAO,CAAC;QACrB;;WAEG;QACH,WAAW,CAAC,EAAE;YAAE,SAAS,EAAE,MAAM,CAAC;YAAC,OAAO,EAAE,MAAM,CAAA;SAAE,EAAE,CAAC;KACxD,CAAC;IACF;;OAEG;IACH,2BAA2B,EAAE;QAC3B,OAAO,EAAE,OAAO,CAAC;QACjB,SAAS,EAAE,OAAO,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,GAAG,SAAS,CAAC;QAC7B,SAAS,EAAE;YACT,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,SAAS,EAAE,OAAO,CAAC;KACpB,CAAC;IACF,gBAAgB,EAAE;QAChB,OAAO,EAAE,OAAO,CAAC;QACjB,SAAS,EAAE;YACT,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;SAC/B,CAAC;QACF,SAAS,EAAE,OAAO,CAAC;KACpB,CAAC;IACF,MAAM,EAAE;QACN,UAAU,EAAE,mBAAmB,GAAG,qBAAqB,GAAG,sBAAsB,CAAC;QACjF,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACzC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7B,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACtC,oBAAoB,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5C,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC,kBAAkB,EAAE;QAClB,YAAY,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,MAAM,YAAY,IAAI;KACnD,CAAC,IAAI,CAAC,GAAG;QACR,IAAI,EAAE,MAAM,CAAC;QACb,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;KACvB;CACF,CAAC,CAAC,CAAC,CAAC;AAEL,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,MAAM,YAAY,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAEjF,qBAAa,gBAAgB;IAC3B,SAAS,EAAE,SAAS,CAAC;IAErB,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM;;;;IAenC,iBAAiB,CAAC,IAAI,EAAE,MAAM;IAI9B,IAAI,CAAC,CAAC,SAAS,MAAM,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;IAejE,EAAE,CAAC,CAAC,SAAS,MAAM,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;IAK7F,GAAG,CAAC,CAAC,SAAS,MAAM,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;gBAKlF,EAAE,EAAE,MAAM,GAAG,IAAI;CAI9B"}