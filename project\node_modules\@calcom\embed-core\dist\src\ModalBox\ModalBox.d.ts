import { EmbedElement } from "../EmbedElement";
export declare class ModalBox extends EmbedElement {
    static htmlOverflow: string;
    static get observedAttributes(): string[];
    show(show: boolean): void;
    open(): void;
    private isLoaderRunning;
    private explicitClose;
    close(): void;
    hideIframe(): void;
    showIframe(): void;
    getErrorElement(): HTMLElement;
    attributeChangedCallback(name: string, oldValue: string, newValue: string): void;
    connectedCallback(): void;
    constructor();
}
//# sourceMappingURL=ModalBox.d.ts.map