(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))i(n);new MutationObserver(n=>{for(const e of n)if(e.type==="childList")for(const s of e.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&i(s)}).observe(document,{childList:!0,subtree:!0});function o(n){const e={};return n.integrity&&(e.integrity=n.integrity),n.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?e.credentials="include":n.crossOrigin==="anonymous"?e.credentials="omit":e.credentials="same-origin",e}function i(n){if(n.ep)return;n.ep=!0;const e=o(n);fetch(n.href,e)}})();const f="https://app.cal.com",w="https://app.cal.com/embed/embed.js";if(window.self===window.top||!document.referrer.startsWith(f))throw new Error(`This page can only be accessed within an iframe from ${f}`);function b(r){try{const o=new URL(r);if(o.protocol!=="http:"&&o.protocol!=="https:")return!1;if(o.hostname==="localhost"||o.hostname==="127.0.0.1")return!0;const i=new URL(f),n=new URL(w),e=t(o.hostname),s=t(i.hostname),a=t(n.hostname);return[s,a].includes(e)}catch{return!1}function t(o){return o.split(".").slice(-2).join(".")}}const u=new URL(document.URL).searchParams,m=u.get("embedType"),d=u.get("calLink"),p=u.get("bookerUrl"),h=u.get("embedLibUrl");if(!p||!h)throw new Error(`Can't Preview: Missing "bookerUrl" or "embedLibUrl" query parameter`);if(!b(h))throw new Error('Invalid "embedLibUrl".');if(!b(p))throw new Error('Invalid "bookerUrl".');if(!d)throw new Error('Missing "calLink" query parameter');(function(r,t,o){const i=function(e,s){e.q.push(s)},n=r.document;r.Cal=r.Cal||function(){const e=r.Cal,s=arguments;if(e.loaded||(e.ns={},e.q=e.q||[],n.head.appendChild(n.createElement("script")).src=t,e.loaded=!0),s[0]===o){const a=function(){i(a,arguments)},l=s[1];a.q=a.q||[],typeof l=="string"?(e.ns[l]=e.ns[l]||a,i(e.ns[l],s),i(e,["initNamespace",l])):i(e,s);return}i(e,s)}})(window,h,"init");const c=window;c.Cal.fingerprint="90a36777d8";c.Cal.version="1.5.3";c.Cal("init",{origin:p});if(m==="inline")c.Cal("inline",{elementOrSelector:"#my-embed",calLink:d});else if(m==="floating-popup")c.Cal("floatingButton",{calLink:d,attributes:{id:"my-floating-button"}});else if(m==="element-click"){const r=document.createElement("button");r.setAttribute("data-cal-link",d),r.innerHTML="I am a button that exists on your website",document.body.appendChild(r)}c.addEventListener("message",r=>{const t=r.data;if(t.mode!=="cal:preview")return;const o=window.Cal;if(!o)throw new Error("Cal is not defined yet");if(t.type=="instruction"&&o(t.instruction.name,t.instruction.arg),t.type=="inlineEmbedDimensionUpdate"){const i=document.querySelector("#my-embed");i&&(i.style.width=t.data.width,i.style.height=t.data.height)}});function g(){const r=window.matchMedia("(prefers-color-scheme: dark)");function t(o){o.matches?(document.body.classList.remove("light"),document.body.classList.add("dark")):(document.body.classList.add("light"),document.body.classList.remove("dark"))}r.addEventListener("change",t),t(new MediaQueryListEvent("change",{matches:r.matches}))}g();
