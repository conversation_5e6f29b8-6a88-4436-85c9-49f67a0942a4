(function(n,i){typeof exports=="object"&&typeof module<"u"?i(exports):typeof define=="function"&&define.amd?define(["exports"],i):(n=typeof globalThis<"u"?globalThis:n||self,i(n.snippet={}))})(this,function(n){"use strict";const i="https://app.cal.com/embed/embed.js";function u(c=i){(function(s,f,r){let o=function(e,t){e.q.push(t)},a=s.document;s.Cal=s.Cal||function(){let e=s.Cal,t=arguments;if(e.loaded||(e.ns={},e.q=e.q||[],a.head.appendChild(a.createElement("script")).src=f,e.loaded=!0),t[0]===r){const p=function(){o(p,arguments)},d=t[1];p.q=p.q||[],typeof d=="string"?(e.ns[d]=e.ns[d]||p,o(e.ns[d],t),o(e,["initNamespace",d])):o(e,t);return}o(e,t)}})(window,c,"init");/*!  Copying ends here. */return window.Cal}const l=u.toString();n.EmbedSnippetString=l,n.default=u,Object.defineProperties(n,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
