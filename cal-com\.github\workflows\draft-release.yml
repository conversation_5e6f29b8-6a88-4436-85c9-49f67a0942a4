name: Draft release
run-name: Draft release ${{ inputs.next_version }}

on:
  workflow_dispatch:
    inputs:
      next_version:
        required: true
        type: string
        description: 'Version name'

permissions:
  contents: write

jobs:
  draft_release:
    runs-on: ubuntu-latest

    steps:
      
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: 'main'
          token: ${{ secrets.GH_ACCESS_TOKEN }}

      - name: Configure git
        run: |
          # Define authorized users and their emails using organization secret
          # Expected format: JSON object with username -> email mapping
          # Example: {"zomars": "<EMAIL>", "peer": "<EMAIL>", "username3": "<EMAIL>"}
          USER_EMAILS='${{ secrets.RELEASE_USER_EMAILS }}'
          
          # Extract email for the triggering user
          USER_EMAIL=$(echo "$USER_EMAILS" | jq -r --arg user "${{ github.actor }}" '.[$user] // empty')
          
          # Fail if user is not authorized or not in mapping
          if [ -z "$USER_EMAIL" ] || [ "$USER_EMAIL" = "null" ]; then
            echo "Error: User '${{ github.actor }}' is not authorized to run the release workflow."
            echo "Only authorized team members can trigger releases."
            echo "Contact your administrator to be added to the authorized users list."
            exit 1
          fi
          
          git config --local user.email "$USER_EMAIL"
          git config --local user.name "${{ github.actor }}"

      - uses: ./.github/actions/yarn-install

      - name: Bump version
        run: |
          cd apps/web
          yarn version ${{ inputs.next_version }}

      - name: Commit changes
        run: |
          git add .
          git commit -m "chore: release v${{ inputs.next_version }}"
          git push

      - name: Draft release
        run: gh release create v$VERSION --generate-notes --draft
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ inputs.next_version }}
