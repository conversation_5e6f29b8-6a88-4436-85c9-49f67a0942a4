<html>
  <head>
    <style>
      .row {
        display: flex;
      }
      .cell-1 {
        border-right: 1px solid #ded9d9;
        padding-right: 10px;
      }

      .cell-2 {
        margin: 10px;
      }
      .dark {
        background-color: rgb(16 16 16);
      }
    </style>
    <script>
      const searchParams = new URL(document.URL).searchParams;
      const embedType = searchParams.get("embedType");
      const calLink = searchParams.get("calLink");
    </script>
    <script type="module" crossorigin src="/embed/preview.js"></script>
  </head>
  
  <body>
    <div id="my-embed" style="width: 100%; height: 90%; overflow: scroll"></div>
  </body>
</html>
