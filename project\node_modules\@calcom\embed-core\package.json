{"name": "@calcom/embed-core", "version": "1.5.3", "description": "This is the vanilla JS core script that embeds Cal Link", "main": "./dist/embed/embed.js", "types": "./dist/index.d.ts", "license": "SEE LICENSE IN LICENSE", "repository": {"type": "git", "url": "https://github.com/calcom/cal.com", "directory": "packages/embeds/embed-core"}, "scripts": {"embed-dev": "yarn workspace @calcom/embed-core dev", "embed-web-start": "yarn workspace @calcom/web start", "__build": "yarn tailwind && vite build && tsc --emitDeclarationOnly --declarationDir dist && cp -r ../../../apps/web/public/embed ./dist/", "__dev": "yarn tailwind && vite build --mode development", "build": "rm -rf dist && NEXT_PUBLIC_EMBED_FINGER_PRINT=$(git rev-parse --short HEAD) NEXT_PUBLIC_EMBED_VERSION=$(node -p 'require(\"./package.json\").version') yarn __build", "build-preview": "PREVIEW_BUILD=1 yarn __build ", "vite": "vite", "tailwind": "yarn tailwindcss -i ./src/styles.css -o ./src/tailwind.generated.css", "buildWatchAndServer": "run-p '__dev' 'vite --port 3100 --strict-port --host --open'", "buildWatchAndServer-https": "run-p '__dev' 'vite --port 3100 --strict-port --host --open --https'", "dev": "yarn tailwind && run-p 'tailwind --watch' 'buildWatchAndServer'", "buildWatchAndServer-no-auto-open": "run-p '__dev' 'vite --port 3100 --strict-port --host'", "dev-no-open": "yarn tailwind && run-p 'tailwind --watch' 'buildWatchAndServer-no-auto-open'", "dev-https": "yarn tailwind && run-p 'tailwind --watch' 'buildWatchAndServer-https'", "dev-real": "vite dev --port 3100", "type-check": "tsc --pretty --noEmit", "type-check:ci": "tsc-absolute --pretty --noEmit", "lint": "eslint --ext .ts,.js src", "lint:fix": "eslint --ext .ts,.js src --fix", "embed-tests": "yarn playwright test --config=playwright/config/playwright.config.ts", "embed-tests-quick": "QUICK=true yarn embed-tests", "embed-tests-update-snapshots:ci": "yarn embed-tests-quick --update-snapshots", "withEmbedPublishEnv": "NEXT_PUBLIC_EMBED_LIB_URL='https://app.cal.com/embed/embed.js' NEXT_PUBLIC_WEBAPP_URL='https://app.cal.com' yarn", "prepack": "yarn ../../../ lint --filter='@calcom/embed-core' && yarn withEmbedPublishEnv build", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf ../../../apps/web/public/embed"}, "files": ["dist"], "postcss": {"map": false, "plugins": {"tailwindcss": {}, "autoprefixer": {}}}, "devDependencies": {"@playwright/test": "^1.45.3", "@vitejs/plugin-basic-ssl": "^1.1.0", "autoprefixer": "^10.4.12", "npm-run-all": "^4.1.5", "postcss": "^8.4.18", "tailwindcss": "^3.3.3", "typescript": "^4.9.4", "vite": "^4.1.2", "vite-plugin-environment": "^1.1.3"}}