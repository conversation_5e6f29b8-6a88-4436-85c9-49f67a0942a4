import type { AllPossibleLayouts, EmbedPageType } from "../types";
declare const html: ({ layout, pageType, }: {
    layout?: AllPossibleLayouts | undefined;
    pageType: EmbedPageType | null;
}) => string;
export declare const getSkeletonData: ({ layout, pageType, }: {
    layout: AllPossibleLayouts;
    pageType: EmbedPageType | null;
}) => {
    skeletonContent: string;
    skeletonContainerStyle: string;
    skeletonStyle: string;
};
export default html;
//# sourceMappingURL=inlineHtml.d.ts.map