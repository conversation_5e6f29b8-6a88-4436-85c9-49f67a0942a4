name: "Pull Request Labeler"
on:
  pull_request_target:
  workflow_dispatch:
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
jobs:
  labeler:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
  team-labels:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: equitybee/team-label-action@main
        with:
          repo-token: ${{ secrets.EQUITY_BEE_TEAM_LABELER_ACTION_TOKEN }}
          organization-name: calcom
          ignore-labels: "admin, app-store, ai, authentication, automated-testing, billing, bookings, caldav, calendar-apps, ci, console, crm-apps, dba, devops, docs, documentation, emails, embeds, event-types, i18n, impersonation, manual-testing, ui, performance, ops-stack, organizations, public-api, routing-forms, seats, teams, webhooks, workflows, zapier"
  apply-labels-from-issue:
    runs-on: ubuntu-latest

    permissions:
      contents: none
      issues: read
      pull-requests: write

    steps:
      - name: Apply labels from linked issue to PR
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            async function getLinkedIssues(owner, repo, prNumber) {
              const query = `query GetLinkedIssues($owner: String!, $repo: String!, $prNumber: Int!) {
                repository(owner: $owner, name: $repo) {
                  pullRequest(number: $prNumber) {
                    closingIssuesReferences(first: 10) {
                      nodes {
                        number
                        labels(first: 10) {
                          nodes {
                            name
                          }
                        }
                      }
                    }
                  }
                }
              }`;

              const variables = {
                owner: owner,
                repo: repo,
                prNumber: prNumber,
              };

              const result = await github.graphql(query, variables);
              return result.repository.pullRequest.closingIssuesReferences.nodes;
            }

            const pr = context.payload.pull_request;
            const linkedIssues = await getLinkedIssues(
              context.repo.owner,
              context.repo.repo,
              pr.number
            );

            const labelsToAdd = new Set();
            for (const issue of linkedIssues) {
              if (issue.labels && issue.labels.nodes) {
                for (const label of issue.labels.nodes) {
                  labelsToAdd.add(label.name);
                }
              }
            }

            if (labelsToAdd.size) {
              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: pr.number,
                labels: Array.from(labelsToAdd),
              });
            }
