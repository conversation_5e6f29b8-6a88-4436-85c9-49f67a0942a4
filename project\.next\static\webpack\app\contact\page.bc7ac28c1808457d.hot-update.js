"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./app/contact/page.tsx":
/*!******************************!*\
  !*** ./app/contact/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _calcom_embed_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @calcom/embed-react */ \"(app-pages-browser)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Select components are not used in this file as we're using native select\n\n\n\n\n\n\n// Wrapper component that uses searchParams\nfunction ContactForm() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        services: [],\n        message: ''\n    });\n    // State to track which category is expanded\n    const [expandedCategory, setExpandedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle URL query parameters on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            const serviceParam = searchParams ? searchParams.get('service') : null;\n            if (serviceParam) {\n                // If the service is a chatbot option, expand the chatbot category\n                if (serviceParam === 'chatbot' || serviceParam.startsWith('chatbot-')) {\n                    setExpandedCategory('chatbot');\n                    // If it's a specific chatbot service, select it\n                    if (serviceParam === 'chatbot-mvp' || serviceParam === 'chatbot-custom') {\n                        setFormData({\n                            \"ContactForm.useEffect\": (prev)=>({\n                                    ...prev,\n                                    services: [\n                                        serviceParam\n                                    ]\n                                })\n                        }[\"ContactForm.useEffect\"]);\n                    }\n                } else if (serviceParam === 'webdev' || serviceParam.startsWith('webdev-')) {\n                    setExpandedCategory('webdev');\n                    // If it's a specific web development service, select it\n                    if ([\n                        'webdev-essential',\n                        'webdev-business',\n                        'webdev-advanced'\n                    ].includes(serviceParam)) {\n                        setFormData({\n                            \"ContactForm.useEffect\": (prev)=>({\n                                    ...prev,\n                                    services: [\n                                        serviceParam\n                                    ]\n                                })\n                        }[\"ContactForm.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"ContactForm.useEffect\"], [\n        searchParams\n    ]);\n    // Function to handle category selection\n    const handleCategorySelect = (category)=>{\n        if (expandedCategory === category) {\n            // If clicking the same category, collapse it\n            setExpandedCategory(null);\n        } else {\n            // Expand the clicked category\n            setExpandedCategory(category);\n        }\n        // Clear error for service field when category changes\n        if (formErrors.service) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors.service;\n                return newErrors;\n            });\n        }\n    };\n    // Function to handle service option selection (supports multiple, but only one per category)\n    const handleServiceSelect = (service)=>{\n        setFormData((prev)=>{\n            const isSelected = prev.services.includes(service);\n            let newServices = [\n                ...prev.services\n            ];\n            if (isSelected) {\n                // Remove if already selected\n                newServices = newServices.filter((s)=>s !== service);\n            } else {\n                // Add the new service, but first remove any other service from the same category\n                if (service.startsWith('chatbot-')) {\n                    // Remove any existing chatbot services\n                    newServices = newServices.filter((s)=>!s.startsWith('chatbot-'));\n                } else if (service.startsWith('webdev-')) {\n                    // Remove any existing webdev services\n                    newServices = newServices.filter((s)=>!s.startsWith('webdev-'));\n                }\n                // Add the new service\n                newServices.push(service);\n            }\n            return {\n                ...prev,\n                services: newServices\n            };\n        });\n        // Clear error for service field when value changes\n        if (formErrors.service) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors.service;\n                return newErrors;\n            });\n        }\n    };\n    // Function to remove a selected service\n    const removeService = (serviceToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                services: prev.services.filter((service)=>service !== serviceToRemove)\n            }));\n    };\n    // Add CSS animation for the interactive service selection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            // Add the animation CSS if it doesn't exist\n            if (!document.getElementById('service-animation-styles')) {\n                const styleEl = document.createElement('style');\n                styleEl.id = 'service-animation-styles';\n                styleEl.textContent = \"\\n        @keyframes fadeIn {\\n          from { opacity: 0; transform: translateY(-10px); }\\n          to { opacity: 1; transform: translateY(0); }\\n        }\\n        .animate-fadeIn {\\n          animation: fadeIn 0.3s ease-out forwards;\\n        }\\n      \";\n                document.head.appendChild(styleEl);\n            }\n            return ({\n                \"ContactForm.useEffect\": ()=>{\n                    // Clean up the style element when component unmounts\n                    const styleEl = document.getElementById('service-animation-styles');\n                    if (styleEl) styleEl.remove();\n                }\n            })[\"ContactForm.useEffect\"];\n        }\n    }[\"ContactForm.useEffect\"], []);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // For general API errors\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormErrors({}); // Clear previous errors\n        setSubmitError('');\n        setSubmitSuccess(false);\n        // --- Validation ---\n        const errors = {};\n        if (!formData.name.trim()) errors.name = 'Name is required.';\n        if (!formData.email.trim()) {\n            errors.email = 'Email is required.';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            errors.email = 'Email is invalid.';\n        }\n        if (formData.services.length === 0) errors.service = 'Please select at least one service.';\n        if (!formData.message.trim()) errors.message = 'Message is required.';\n        if (Object.keys(errors).length > 0) {\n            setFormErrors(errors);\n            return; // Stop submission if validation fails\n        }\n        // --- End Validation ---\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    service: formData.services.join(', ') // Convert array to string for backward compatibility\n                })\n            });\n            if (!response.ok) {\n                let errorMessage = 'Failed to submit form';\n                try {\n                    const errorData = await response.json();\n                    errorMessage = errorData.error || errorMessage;\n                } catch (e) {\n                // If response is not JSON, use default message\n                }\n                throw new Error(errorMessage);\n            }\n            await response.json(); // Consume the response\n            setSubmitSuccess(true);\n            setFormData({\n                name: '',\n                email: '',\n                services: [],\n                message: ''\n            });\n        } catch (error) {\n            console.error('Contact form submission error:', error);\n            setSubmitError(error instanceof Error ? error.message : 'Something went wrong. Please try again.');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear the error for the field being changed\n        if (formErrors[name]) {\n            setFormErrors((prevErrors)=>{\n                const newErrors = {\n                    ...prevErrors\n                };\n                delete newErrors[name];\n                return newErrors;\n            });\n        }\n    };\n    // We're using the native select element with the handleChange function\n    // Use useRef to persist initialization state across re-renders\n    const isInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Cal.com embed initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactForm.useEffect\": ()=>{\n            ({\n                \"ContactForm.useEffect\": async function() {\n                    const cal = await (0,_calcom_embed_react__WEBPACK_IMPORTED_MODULE_7__.getCalApi)();\n                    cal(\"ui\", {\n                        \"theme\": \"dark\",\n                        \"styles\": {\n                            \"branding\": {\n                                \"brandColor\": \"#7c3aed\"\n                            }\n                        },\n                        \"hideEventTypeDetails\": false\n                    });\n                }\n            })[\"ContactForm.useEffect\"]();\n        }\n    }[\"ContactForm.useEffect\"], []);\n    // ORIGINAL CALENDLY CODE - COMMENTED OUT\n    /*\r\n  useEffect(() => {\r\n  //   const initCalendly = () => {\r\n  //     if (isInitializedRef.current) {\r\n  //       console.log('⚠️ Calendly already initialized, skipping...');\r\n  //       return;\r\n  //     }\r\n\r\n  //     const widgetElement = document.querySelector('.calendly-inline-widget');\r\n  //     if (!widgetElement) {\r\n  //       console.warn('⚠️ Calendly widget element not found');\r\n  //       return;\r\n  //     }\r\n\r\n  //     // Clear any existing content to prevent duplicates\r\n  //     widgetElement.innerHTML = `\r\n  //       <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //         <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mb-4\"></div>\r\n  //         <p class=\"text-purple-100/70 mb-4\">Loading calendar...</p>\r\n  //       </div>\r\n  //     `;\r\n\r\n  //     if (!window.Calendly) {\r\n  //       console.warn('⚠️ Calendly script not loaded yet, retrying...');\r\n  //       setTimeout(initCalendly, 500);\r\n  //       return;\r\n  //     }\r\n\r\n  //     try {\r\n  //       console.log('🔄 Attempting to initialize Calendly inline widget...');\r\n  //       console.log('📍 Widget element:', widgetElement);\r\n  //       console.log('🌐 Calendly object:', window.Calendly);\r\n\r\n  //       // Clear the loading message\r\n  //       widgetElement.innerHTML = '';\r\n\r\n  //       window.Calendly.initInlineWidget({\r\n  //         url: 'https://calendly.com/pilybas-edgaras/30min',\r\n  //         parentElement: widgetElement as HTMLElement,\r\n  //         prefill: {},\r\n  //         utm: {}\r\n  //       });\r\n  //       isInitializedRef.current = true;\r\n  //       console.log('✅ Calendly inline widget initialized successfully');\r\n  //     } catch (error) {\r\n  //       console.error('❌ Calendly initialization failed:', error);\r\n  //       // Show error message to user\r\n  //       widgetElement.innerHTML = `\r\n  //         <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //           <p class=\"text-red-400 mb-4\">Failed to load calendar</p>\r\n  //           <p class=\"text-purple-100/70 text-sm\">Please try refreshing the page</p>\r\n  //         </div>\r\n  //       `;\r\n  //     }\r\n  //   };\r\n\r\n  //   // Check if Calendly script is already loaded\r\n  //   if (window.Calendly) {\r\n  //     console.log('✅ Calendly already loaded, initializing...');\r\n  //     initCalendly();\r\n  //     return;\r\n  //   }\r\n\r\n  //   // Check if script is already in DOM\r\n  //   const existingScript = document.querySelector('script[src*=\"calendly.com\"]');\r\n  //   if (existingScript) {\r\n  //     console.log('⏳ Calendly script already in DOM, waiting for load...');\r\n  //     existingScript.addEventListener('load', initCalendly);\r\n  //     return;\r\n  //   }\r\n\r\n  //   // Load Calendly script only once\r\n  //   console.log('📥 Loading Calendly script...');\r\n  //   const script = document.createElement('script');\r\n  //   script.src = 'https://assets.calendly.com/assets/external/widget.js';\r\n  //   script.async = true;\r\n  //   script.id = 'calendly-script'; // Add ID to prevent duplicates\r\n\r\n  //   script.onload = () => {\r\n  //     console.log('✅ Calendly script loaded successfully');\r\n  //     setTimeout(initCalendly, 100);\r\n  //   };\r\n\r\n  //   script.onerror = (error) => {\r\n  //     console.error('❌ Failed to load Calendly script:', error);\r\n  //     // Show fallback message\r\n  //     const widgetElement = document.querySelector('.calendly-inline-widget');\r\n  //     if (widgetElement) {\r\n  //       widgetElement.innerHTML = `\r\n  //         <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-8\">\r\n  //           <p class=\"text-red-400 mb-4\">Unable to load calendar</p>\r\n  //           <p class=\"text-purple-100/70 text-sm mb-4\">Please book directly:</p>\r\n  //           <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\r\n  //              class=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\">\r\n  //             Open Calendar in New Tab\r\n  //           </a>\r\n  //         </div>\r\n  //       `;\r\n  //     }\r\n  //   };\r\n\r\n  //   document.head.appendChild(script);\r\n\r\n  //   return () => {\r\n  //     // Cleanup - but don't reset isInitializedRef as it should persist\r\n  //     // The widget will be cleaned up when the component unmounts\r\n  //   };\r\n  // }, []); // Empty dependency array to run only once\r\n  */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl font-bold text-white mb-4\",\n                                            children: \"Do not hesitate to get in touch\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-purple-100/70\",\n                                            children: \"We will do our best to answer all your questions and provide you with our services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"+31 610 271 038\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100/70\",\n                                                            children: \"Laplace-gebouw, Laplace 32, 5612 AJ Eindhoven\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"contact-form\",\n                                className: \"bg-white rounded-2xl p-8 shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                        children: \"Contact us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading contact form...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"contact-form\",\n                                className: \"bg-white rounded-2xl p-8 shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                        children: \"Contact us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        className: \"space-y-6\",\n                                        onSubmit: handleSubmit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"name\",\n                                                        name: \"name\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors\",\n                                                        placeholder: \"Your name\",\n                                                        value: formData.name,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 39\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors\",\n                                                        type: \"email\",\n                                                        placeholder: \"<EMAIL>\",\n                                                        value: formData.email,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Service\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-3 relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-purple-500/5 rounded-full blur-xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-6 -left-6 w-12 h-12 bg-blue-500/5 rounded-full blur-xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'chatbot' ? 'ring-4 ring-purple-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>handleCategorySelect('chatbot'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-purple-600 to-pink-500 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 492,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 493,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Chatbot Integration\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 495,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M8 10L12 14L16 10\",\n                                                                                                        stroke: \"currentColor\",\n                                                                                                        strokeWidth: \"2\",\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                        lineNumber: 498,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 497,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 496,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 494,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"AI-powered chat solutions\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 502,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 490,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-purple-600/10 to-pink-500/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'webdev' ? 'ring-4 ring-blue-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>handleCategorySelect('webdev'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-blue-600 to-cyan-500 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 516,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 517,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Web Development\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 519,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M8 10L12 14L16 10\",\n                                                                                                        stroke: \"currentColor\",\n                                                                                                        strokeWidth: \"2\",\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                        lineNumber: 522,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 521,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 520,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"Custom website solutions\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 526,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 to-cyan-500/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] \".concat(expandedCategory === 'other' || formData.services.includes('other') ? 'ring-4 ring-gray-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'),\n                                                                        onClick: ()=>{\n                                                                            handleCategorySelect('other');\n                                                                            handleServiceSelect('other');\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-br from-gray-700 to-gray-600 p-4 text-white relative overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 543,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold\",\n                                                                                                children: \"Other Services\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 546,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"bg-white/20 rounded-full p-1\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    width: \"16\",\n                                                                                                    height: \"16\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    fill: \"none\",\n                                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M12 5V19\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                            lineNumber: 549,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M5 12H19\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                            lineNumber: 550,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                    lineNumber: 548,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                                lineNumber: 547,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs mt-1 text-white/80\",\n                                                                                        children: \"Custom requirements\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                        lineNumber: 554,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-br from-gray-700/10 to-gray-600/10 pointer-events-none\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                lineNumber: 558,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            expandedCategory === 'chatbot' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pl-4 border-l-4 border-purple-500 space-y-3 animate-fadeIn mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('chatbot-mvp') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('chatbot-mvp'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('chatbot-mvp') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"MVP Virtual Assistant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 572,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Essential chatbot features for your business\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 573,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('chatbot-custom') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('chatbot-custom'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('chatbot-custom') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Customizable AI Assistant\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 585,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Advanced features with deep integrations\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 586,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            expandedCategory === 'webdev' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"pl-4 border-l-4 border-blue-500 space-y-3 animate-fadeIn mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-essential') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-essential'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-essential') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 601,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Website Essentials\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 603,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Basic website with core functionality\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-business') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-business'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-business') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 614,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Smart Business Websites\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 616,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Enhanced features for growing businesses\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md \".concat(formData.services.includes('webdev-advanced') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''),\n                                                                        onClick: ()=>handleServiceSelect('webdev-advanced'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-5 h-5 rounded-full mr-2 \".concat(formData.services.includes('webdev-advanced') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-gray-800\",\n                                                                                            children: \"Advanced Web Platforms\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 629,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: \"Complex solutions with custom functionality\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                            lineNumber: 630,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 622,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.service && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.service\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.services.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Selected Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.services.map((service)=>{\n                                                            const serviceLabels = {\n                                                                'chatbot-mvp': 'MVP Virtual Assistant',\n                                                                'chatbot-custom': 'Customizable AI Assistant',\n                                                                'webdev-essential': 'Website Essentials',\n                                                                'webdev-business': 'Smart Business Websites',\n                                                                'webdev-advanced': 'Advanced Web Platforms',\n                                                                'other': 'Other Services'\n                                                            };\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: serviceLabels[service] || service\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeService(service),\n                                                                        className: \"ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors duration-200\",\n                                                                        \"aria-label\": \"Remove \".concat(serviceLabels[service] || service),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"14\",\n                                                                            height: \"14\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M18 6L6 18\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    strokeWidth: \"2\",\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M6 6L18 18\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    strokeWidth: \"2\",\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, service, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Message\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                        id: \"message\",\n                                                        name: \"message\",\n                                                        className: \"border-gray-300 focus:border-purple-500 focus:ring-purple-500 min-h-[150px] transition-colors\",\n                                                        placeholder: \"Your message\",\n                                                        value: formData.message,\n                                                        onChange: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 mt-1\",\n                                                        children: [\n                                                            \" \",\n                                                            formErrors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs\",\n                                                                children: formErrors.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 15\n                                            }, this),\n                                            submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: submitError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center justify-center space-y-2 animate-fade-in\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-20 h-20 text-green-500 animate-outgoing-pop\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-500 text-sm transition-opacity duration-500 ease-out\",\n                                                        children: \"Thank you! Your message has been sent.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105\",\n                                                disabled: isSubmitting,\n                                                children: isSubmitting ? 'Sending...' : 'Contact'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative my-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t-2 border-purple-500/50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 731,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 px-6 py-2 text-3xl font-bold text-white rounded-full shadow-lg\",\n                                children: \"OR!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 730,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Schedule a Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-purple-100/70\",\n                                    children: \"Loading calendar...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 11\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"Schedule a Meeting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-purple-100/70\",\n                                        children: \"Book a free consultation call with our team\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-900/30 backdrop-blur-sm rounded-2xl p-0 border border-purple-700/20 w-full max-w-5xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"calendly-inline-widget rounded-2xl overflow-hidden\",\n                                        \"data-url\": \"https://calendly.com/pilybas-edgaras/30min\",\n                                        style: {\n                                            minWidth: \"320px\",\n                                            height: \"700px\",\n                                            width: \"100%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 752,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 742,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 382,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"Pk26HUivDWhh4rA0nWLhSq6M94s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ContactForm;\n// Main page component that wraps ContactForm in Suspense\nfunction ContactPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 781,\n                columnNumber: 7\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 780,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactForm, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n            lineNumber: 783,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 780,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ContactPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ContactForm\");\n$RefreshReg$(_c1, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contact/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@calcom/embed-react/dist/Cal.es.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ R),\n/* harmony export */   getCalApi: () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\n\nconst b = \"https://app.cal.com/embed/embed.js\";\nfunction m(s = b) {\n  (function(r, e, l) {\n    let t = function(n, i) {\n      n.q.push(i);\n    }, o = r.document;\n    r.Cal = r.Cal || function() {\n      let n = r.Cal, i = arguments;\n      if (n.loaded || (n.ns = {}, n.q = n.q || [], o.head.appendChild(o.createElement(\"script\")).src = e, n.loaded = !0), i[0] === l) {\n        const u = function() {\n          t(u, arguments);\n        }, c = i[1];\n        u.q = u.q || [], typeof c == \"string\" ? (n.ns[c] = n.ns[c] || u, t(n.ns[c], i), t(n, [\"initNamespace\", c])) : t(n, i);\n        return;\n      }\n      t(n, i);\n    };\n  })(\n    window,\n    //! Replace it with \"https://cal.com/embed.js\" or the URL where you have embed.js installed\n    s,\n    \"init\"\n  );\n  /*!  Copying ends here. */\n  return window.Cal;\n}\nm.toString();\nfunction q(s) {\n  const [r, e] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    e(() => m(s));\n  }, []), r;\n}\nconst h = function(r) {\n  const {\n    calLink: e,\n    calOrigin: l,\n    namespace: t = \"\",\n    config: o,\n    initConfig: n = {},\n    embedJsUrl: i,\n    ...u\n  } = r;\n  if (!e)\n    throw new Error(\"calLink is required\");\n  const c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1), a = q(i), f = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!a || c.current || !f.current)\n      return;\n    c.current = !0;\n    const d = f.current;\n    t ? (a(\"init\", t, {\n      ...n,\n      origin: l\n    }), a.ns[t](\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    })) : (a(\"init\", {\n      ...n,\n      origin: l\n    }), a(\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    }));\n  }, [a, e, o, t, l, n]), a ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    ref: f,\n    ...u\n  }) : null;\n}, R = h;\nfunction j(s) {\n  const r = typeof s == \"string\" ? { embedJsUrl: s } : s ?? {}, { namespace: e = \"\", embedJsUrl: l } = r;\n  return new Promise(function t(o) {\n    const n = m(l);\n    n(\"init\", e);\n    const i = e ? n.ns[e] : n;\n    if (!i) {\n      setTimeout(() => {\n        t(o);\n      }, 50);\n      return;\n    }\n    o(i);\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\n"));

/***/ })

});