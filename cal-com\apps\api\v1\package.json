{"name": "@calcom/api", "version": "1.0.0", "description": "Public API for Cal.com", "main": "index.ts", "repository": "**************:calcom/api.git", "author": "Cal.com Inc.", "private": true, "scripts": {"build": "next build", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf .next", "dev": "PORT=3003 next dev", "lint": "eslint . --ignore-path .gitignore", "lint:fix": "eslint . --ext .ts,.js,.tsx,.jsx --fix", "start": "PORT=3003 next start", "docker-start-api": "PORT=80 next start", "type-check": "tsc --pretty --noEmit", "type-check:ci": "tsc-absolute --pretty --noEmit"}, "devDependencies": {"@calcom/tsconfig": "*", "@calcom/types": "*", "node-mocks-http": "^1.11.0"}, "dependencies": {"@calcom/app-store": "*", "@calcom/dayjs": "*", "@calcom/emails": "*", "@calcom/features": "*", "@calcom/lib": "*", "@calcom/prisma": "*", "@calcom/trpc": "*", "@sentry/nextjs": "^9.15.0", "bcryptjs": "^2.4.3", "memory-cache": "^0.2.0", "next": "^15.3.0", "next-api-middleware": "^1.0.1", "next-axiom": "^0.17.0", "next-swagger-doc": "^0.3.6", "next-validations": "^0.2.0", "typescript": "^5.7.2", "tzdata": "^1.0.30", "uuid": "^8.3.2", "zod": "^3.22.4"}}