name: E2E
on:
  workflow_call:
env:
  ALLOWED_HOSTNAMES: ${{ vars.CI_ALLOWED_HOSTNAMES }}
  API_KEY_PREFIX: ${{ secrets.CI_API_KEY_PREFIX }}
  API_PORT: ${{ vars.CI_API_V2_PORT }}
  CALCOM_LICENSE_KEY: ${{ secrets.CI_CALCOM_LICENSE_KEY }}
  DAILY_API_KEY: ${{ secrets.CI_DAILY_API_KEY }}
  DATABASE_URL: ${{ secrets.CI_DATABASE_URL }}
  DATABASE_READ_URL: ${{ secrets.CI_DATABASE_URL }}
  DATABASE_WRITE_URL: ${{ secrets.CI_DATABASE_URL }}
  DATABASE_DIRECT_URL: ${{ secrets.CI_DATABASE_URL }}
  GOOGLE_API_CREDENTIALS: ${{ secrets.CI_GOOGLE_API_CREDENTIALS }}
  IS_E2E: true
  NEXTAUTH_SECRET: ${{ secrets.CI_NEXTAUTH_SECRET }}
  NEXTAUTH_URL: ${{ secrets.CI_NEXTAUTH_URL }}
  NODE_OPTIONS: --max-old-space-size=29000
  REDIS_URL: "redis://localhost:6379"
  LINGO_DOT_DEV_API_KEY: ${{ secrets.CI_LINGO_DOT_DEV_API_KEY }}
  STRIPE_PRIVATE_KEY: ${{ secrets.CI_STRIPE_PRIVATE_KEY }}
  STRIPE_API_KEY: ${{ secrets.CI_STRIPE_PRIVATE_KEY }}
  STRIPE_CLIENT_ID: ${{ secrets.CI_STRIPE_CLIENT_ID }}
  STRIPE_WEBHOOK_SECRET: ${{ secrets.CI_STRIPE_WEBHOOK_SECRET }}
jobs:
  e2e:
    timeout-minutes: 20
    name: E2E API v2
    runs-on: buildjet-8vcpu-ubuntu-2204
    services:
      postgres:
        image: postgres:13
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: calendso
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      redis:
        image: redis:latest
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    strategy:
      fail-fast: false
    steps:
      - uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - uses: ./.github/actions/cache-db
      - name: Run Tests
        working-directory: apps/api/v2
        run: |
          yarn test:e2e
          EXIT_CODE=$?
          echo "yarn test:e2e command exit code: $EXIT_CODE"
          exit $EXIT_CODE
      - name: Upload Test Results
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: test-results-api-v2
          path: test-results
