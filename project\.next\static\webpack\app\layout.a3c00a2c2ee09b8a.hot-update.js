"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"602102e7c3f5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaW5nYXBcXE9uZURyaXZlXFxTdGFsaW5pcyBrb21waXV0ZXJpc1xcQ29kZV9Qcm9qZWN0c1xcVXBaZXJhXFxVcFplcmEgKG5ld2VzdClcXHByb2plY3RcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2MDIxMDJlN2MzZjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/chatbot/ChatMessage.tsx":
/*!********************************************!*\
  !*** ./components/chatbot/ChatMessage.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./components/chatbot/QuickActions.tsx\");\n/* harmony import */ var _ServiceOptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ServiceOptions */ \"(app-pages-browser)/./components/chatbot/ServiceOptions.tsx\");\n/* harmony import */ var _MainMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MainMenu */ \"(app-pages-browser)/./components/chatbot/MainMenu.tsx\");\n/* harmony import */ var _calcom_embed_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @calcom/embed-react */ \"(app-pages-browser)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Enhanced function to format bot messages with proper spacing and structure\nfunction formatBotMessage(text) {\n    if (!text) return text;\n    // Enhanced text formatting with automatic spacing detection\n    let formattedText = text;\n    // Add line breaks after sentences that end with periods, exclamation marks, or question marks\n    // but only if they're followed by a capital letter or \"For example\" (indicating a new sentence/section)\n    formattedText = formattedText.replace(/([.!?])\\s+([A-Z]|For example)/g, '$1\\n\\n$2');\n    // Add line breaks before \"For example,\" patterns\n    formattedText = formattedText.replace(/\\s+(For example,)/gi, '\\n\\n$1');\n    // Add line breaks after price ranges and before new information\n    formattedText = formattedText.replace(/(€\\d+[,\\d]*(?:\\s*to\\s*€\\d+[,\\d]*)?[.,]?)\\s+([A-Z])/g, '$1\\n\\n$2');\n    // Add line breaks before \"After\" patterns (like \"After a free consultation\")\n    formattedText = formattedText.replace(/\\s+(After\\s+)/gi, '\\n\\n$1');\n    // Add spacing around key pricing phrases that should stand out\n    formattedText = formattedText.replace(/(landing pages start from|full websites range from|chatbot setups typically cost|chatbots typically cost)/gi, '\\n\\n$1');\n    // Add line breaks before concluding statements\n    formattedText = formattedText.replace(/\\s+(After a free consultation,|You'll receive)/gi, '\\n\\n$1');\n    // Split by double newlines to create paragraphs\n    const paragraphs = formattedText.split('\\n\\n');\n    return paragraphs.map((paragraph, pIndex)=>{\n        // Skip empty paragraphs\n        if (!paragraph.trim()) return null;\n        // Split by single newlines within paragraphs\n        const lines = paragraph.split('\\n');\n        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: pIndex > 0 ? 'mt-4' : '',\n            children: lines.map((line, lIndex)=>{\n                // Skip empty lines\n                if (!line.trim()) return null;\n                // Process bold formatting (**text**)\n                const parts = line.split(/(\\*\\*.*?\\*\\*)/g);\n                const formattedLine = parts.map((part, partIndex)=>{\n                    if (part.startsWith('**') && part.endsWith('**')) {\n                        const boldText = part.slice(2, -2);\n                        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-semibold\",\n                            children: boldText\n                        }, partIndex, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 22\n                        }, this));\n                    }\n                    return part;\n                });\n                return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: lIndex > 0 ? 'mt-2' : '',\n                    children: formattedLine\n                }, lIndex, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 13\n                }, this));\n            })\n        }, pIndex, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this));\n    }).filter(Boolean); // Remove null entries\n}\nfunction ChatMessage(param) {\n    let { message, screenSize, onQuickAction } = param;\n    const isUser = message.isUser;\n    // Get screen size properties\n    const isSmall = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.isSmall) || false;\n    const width = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.width) || 1200;\n    // Adjust spacing and sizing based on screen size\n    let messageSpacing = 'mb-6';\n    let avatarSize = 'w-8 h-8';\n    let messagePadding = 'p-3';\n    let maxWidth = 'max-w-[85%]';\n    let dotSize = 'w-2 h-2';\n    if (isSmall) {\n        if (width <= 320) {\n            messageSpacing = 'mb-3';\n            avatarSize = 'w-5 h-5';\n            messagePadding = 'p-1.5';\n            maxWidth = 'max-w-[92%]';\n            dotSize = 'w-1 h-1';\n        } else if (width <= 375) {\n            messageSpacing = 'mb-4';\n            avatarSize = 'w-6 h-6';\n            messagePadding = 'p-2';\n            maxWidth = 'max-w-[90%]';\n            dotSize = 'w-1.5 h-1.5';\n        } else {\n            messageSpacing = 'mb-5';\n            avatarSize = 'w-7 h-7';\n            messagePadding = 'p-2.5';\n            maxWidth = 'max-w-[88%]';\n            dotSize = 'w-1.5 h-1.5';\n        }\n    }\n    if (message.type === 'calendly') {\n        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendlyWidget, {\n            screenSize: screenSize,\n            messageSpacing: messageSpacing\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 127,\n            columnNumber: 12\n        }, this));\n    }\n    if (message.type === 'mainMenu') {\n        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MainMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onMenuClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this));\n    }\n    if (message.type === 'quickActions') {\n        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onActionClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this));\n    }\n    if (message.type === 'serviceOptions') {\n        return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full animate-fade-in \".concat(messageSpacing),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ServiceOptions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onServiceClick: onQuickAction,\n                screenSize: screenSize\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this));\n    }\n    return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start \".concat(isUser ? 'justify-end' : 'justify-start', \" w-full \").concat(messageSpacing),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-2 \".concat(isUser ? 'flex-row-reverse space-x-reverse' : '', \" \").concat(maxWidth),\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo/chatbot_avatar.png\",\n                    alt: \"Bot Avatar\",\n                    className: \"\".concat(avatarSize, \" rounded-full flex-shrink-0\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(isUser ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-tl-xl rounded-tr-xl rounded-bl-xl shadow' : 'bg-gray-100 text-gray-800 rounded-tr-xl rounded-tl-xl rounded-br-xl shadow', \" \").concat(messagePadding, \" min-w-[40px] break-words animate-fade-in responsive-text\"),\n                    children: message.text === '...' && !isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0.2s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"\".concat(dotSize, \" bg-gray-400 rounded-full animate-bounce\"),\n                                style: {\n                                    animationDelay: '0.4s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, this) : isUser ? message.text : formatBotMessage(message.text)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this));\n}\n_c = ChatMessage;\n// Calendly Widget Component\nfunction CalendlyWidget(param) {\n    let { screenSize, messageSpacing } = param;\n    _s();\n    const calendlyRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isSmall = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.isSmall) || false;\n    const width = (screenSize === null || screenSize === void 0 ? void 0 : screenSize.width) || 1200;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CalendlyWidget.useEffect\": ()=>{\n            ({\n                \"CalendlyWidget.useEffect\": async function() {\n                    const cal = await (0,_calcom_embed_react__WEBPACK_IMPORTED_MODULE_5__.getCalApi)();\n                    cal(\"ui\", {\n                        \"theme\": \"light\",\n                        \"styles\": {\n                            \"branding\": {\n                                \"brandColor\": \"#7c3aed\"\n                            }\n                        },\n                        \"hideEventTypeDetails\": true\n                    });\n                }\n            })[\"CalendlyWidget.useEffect\"]();\n        }\n    }[\"CalendlyWidget.useEffect\"], []);\n    // ORIGINAL CALENDLY CODE - COMMENTED OUT\n    /*\n    const initializeCalendly = () => {\n      if (isInitializedRef.current || !calendlyRef.current) {\n        return;\n      }\n\n      try {\n        if (window.Calendly) {\n          // Clear any existing content\n          calendlyRef.current.innerHTML = '';\n\n          // Initialize the widget\n          window.Calendly.initInlineWidget({\n            url: 'https://calendly.com/pilybas-edgaras/30min',\n            parentElement: calendlyRef.current,\n            prefill: {},\n            utm: {}\n          });\n          isInitializedRef.current = true;\n        } else {\n          // Show loading state\n          calendlyRef.current.innerHTML = `\n            <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n              <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-2\"></div>\n              <p class=\"text-gray-600 text-sm\">Loading calendar...</p>\n            </div>\n          `;\n        }\n      } catch (error) {\n        console.error('Calendly initialization error:', error);\n        // Show fallback link\n        //   calendlyRef.current.innerHTML = `\n        //     <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n        //       <p class=\"text-gray-600 text-sm mb-3\">Unable to load calendar widget</p>\n        //       <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\n        //          class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\">\n        //         Open Calendar in New Tab\n        //       </a>\n        //     </div>\n        //   `;\n        // }\n      // }\n    // };\n\n    // // Load Calendly script if not already loaded\n    // if (!window.Calendly) {\n    //   const existingScript = document.querySelector('script[src*=\"calendly.com\"]');\n    //   if (!existingScript) {\n    //     const script = document.createElement('script');\n    //     script.src = 'https://assets.calendly.com/assets/external/widget.js';\n    //     script.async = true;\n    //     script.onload = () => {\n    //       setTimeout(initializeCalendly, 100);\n    //     };\n    //     script.onerror = () => {\n    //       // Show fallback on script load error\n    //       if (calendlyRef.current) {\n    //         calendlyRef.current.innerHTML = `\n    //           <div class=\"flex flex-col items-center justify-center h-full w-full text-center p-4\">\n    //             <p class=\"text-gray-600 text-sm mb-3\">Unable to load calendar</p>\n    //             <a href=\"https://calendly.com/pilybas-edgaras/30min\" target=\"_blank\" rel=\"noopener noreferrer\"\n    //                class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\">\n    //               Book Meeting Directly\n    //             </a>\n    //           </div>\n    //         `;\n    //       }\n    //     };\n    //     document.head.appendChild(script);\n    //   } else {\n    //     existingScript.addEventListener('load', initializeCalendly);\n    //   }\n    // } else {\n    //   // If script is already loaded, initialize immediately\n    //   setTimeout(initializeCalendly, 100);\n    // }\n\n    // return () => {\n    //   // Cleanup: remove any existing Calendly widgets\n    //   if (calendlyRef.current) {\n    //     calendlyRef.current.innerHTML = '';\n    //   }\n    //   isInitializedRef.current = false;\n    // };\n    */ return(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full animate-fade-in \".concat(messageSpacing, \" flex justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-2 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    minWidth: isSmall ? width <= 320 ? '220px' : '240px' : '280px',\n                    height: isSmall ? width <= 320 ? '220px' : '250px' : '300px',\n                    width: '100%'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_calcom_embed_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    calOrigin: \"https://cal.com\",\n                    calLink: \"bailey/15min\",\n                    style: {\n                        width: \"100%\",\n                        height: isSmall ? width <= 320 ? '220px' : '250px' : '300px',\n                        overflow: \"scroll\"\n                    },\n                    config: {\n                        \"theme\": \"light\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\Code_Projects\\\\UpZera\\\\UpZera (newest)\\\\project\\\\components\\\\chatbot\\\\ChatMessage.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this));\n}\n_s(CalendlyWidget, \"sOPyCU/urotnPJk0NImxj6Blkck=\");\n_c1 = CalendlyWidget;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatMessage\");\n$RefreshReg$(_c1, \"CalendlyWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chatbot/ChatMessage.tsx\n"));

/***/ })

});