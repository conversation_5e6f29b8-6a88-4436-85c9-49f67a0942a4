"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@calcom";
exports.ids = ["vendor-chunks/@calcom"];
exports.modules = {

/***/ "(ssr)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@calcom/embed-react/dist/Cal.es.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ R),\n/* harmony export */   getCalApi: () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\nconst b = \"https://app.cal.com/embed/embed.js\";\nfunction m(s = b) {\n  (function(r, e, l) {\n    let t = function(n, i) {\n      n.q.push(i);\n    }, o = r.document;\n    r.Cal = r.Cal || function() {\n      let n = r.Cal, i = arguments;\n      if (n.loaded || (n.ns = {}, n.q = n.q || [], o.head.appendChild(o.createElement(\"script\")).src = e, n.loaded = !0), i[0] === l) {\n        const u = function() {\n          t(u, arguments);\n        }, c = i[1];\n        u.q = u.q || [], typeof c == \"string\" ? (n.ns[c] = n.ns[c] || u, t(n.ns[c], i), t(n, [\"initNamespace\", c])) : t(n, i);\n        return;\n      }\n      t(n, i);\n    };\n  })(\n    window,\n    //! Replace it with \"https://cal.com/embed.js\" or the URL where you have embed.js installed\n    s,\n    \"init\"\n  );\n  /*!  Copying ends here. */\n  return window.Cal;\n}\nm.toString();\nfunction q(s) {\n  const [r, e] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    e(() => m(s));\n  }, []), r;\n}\nconst h = function(r) {\n  const {\n    calLink: e,\n    calOrigin: l,\n    namespace: t = \"\",\n    config: o,\n    initConfig: n = {},\n    embedJsUrl: i,\n    ...u\n  } = r;\n  if (!e)\n    throw new Error(\"calLink is required\");\n  const c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1), a = q(i), f = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!a || c.current || !f.current)\n      return;\n    c.current = !0;\n    const d = f.current;\n    t ? (a(\"init\", t, {\n      ...n,\n      origin: l\n    }), a.ns[t](\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    })) : (a(\"init\", {\n      ...n,\n      origin: l\n    }), a(\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    }));\n  }, [a, e, o, t, l, n]), a ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    ref: f,\n    ...u\n  }) : null;\n}, R = h;\nfunction j(s) {\n  const r = typeof s == \"string\" ? { embedJsUrl: s } : s ?? {}, { namespace: e = \"\", embedJsUrl: l } = r;\n  return new Promise(function t(o) {\n    const n = m(l);\n    n(\"init\", e);\n    const i = e ? n.ns[e] : n;\n    if (!i) {\n      setTimeout(() => {\n        t(o);\n      }, 50);\n      return;\n    }\n    o(i);\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@calcom/embed-react/dist/Cal.es.mjs\n");

/***/ })

};
;