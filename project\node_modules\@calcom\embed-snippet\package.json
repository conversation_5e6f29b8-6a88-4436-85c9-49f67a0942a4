{"name": "@calcom/embed-snippet", "sideEffects": false, "version": "1.3.3", "main": "./dist/snippet.umd.js", "module": "./dist/snippet.es.js", "description": "Vanilla JS embed snippet that is responsible to fetch @calcom/embed-core and thus show Cal Link as an embed on a page.", "license": "SEE LICENSE IN LICENSE", "repository": {"type": "git", "url": "https://github.com/calcom/cal.com", "directory": "packages/embeds/embed-snippet"}, "scripts": {"build": "rm -rf dist && vite build && tsc --emitDeclarationOnly --declarationDir dist", "type-check": "tsc --pretty --noEmit", "type-check:ci": "tsc-absolute --pretty --noEmit", "lint": "eslint --ext .ts,.js src", "withEmbedPublishEnv": "NEXT_PUBLIC_EMBED_LIB_URL='https://app.cal.com/embed/embed.js' NEXT_PUBLIC_WEBAPP_URL='https://app.cal.com' yarn", "prepack": "yarn ../../../ lint --filter='@calcom/embed-snippet' && yarn withEmbedPublishEnv build", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "files": ["dist"], "types": "./dist/index.d.ts", "devDependencies": {"typescript": "^4.9.4", "vite": "^4.1.2"}, "dependencies": {"@calcom/embed-core": "1.5.3"}}