/// <reference types="react" />
import type { PrefillAndIframeAttrsConfig } from "@calcom/embed-core";
type CalProps = {
    calOrigin?: string;
    calLink: string;
    initConfig?: {
        debug?: boolean;
        uiDebug?: boolean;
    };
    namespace?: string;
    config?: PrefillAndIframeAttrsConfig;
    embedJsUrl?: string;
} & React.HTMLAttributes<HTMLDivElement>;
declare const Cal: (props: CalProps) => JSX.Element | null;
export default Cal;
//# sourceMappingURL=Cal.d.ts.map